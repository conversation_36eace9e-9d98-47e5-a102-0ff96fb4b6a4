import subprocess
import signal

def main():
    record_command = ["rec", "-t", "wav", "output.wav"]
    
    print("开始录音... 按 Enter 停止录音")
    process = subprocess.Popen(record_command)
    
    try:
        input("按 Enter 停止录音...")
        process.send_signal(signal.SIGINT)
        process.wait()
        print("录音已停止.")
    except KeyboardInterrupt:
        process.terminate()

if __name__ == "__main__":
    main()