import subprocess
import signal
import os
from datetime import datetime


def main():
    # 创建保存目录
    save_dir = "/Users/<USER>/Downloads/record_audio"
    os.makedirs(save_dir, exist_ok=True)

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(save_dir, f"recording_{timestamp}.wav")

    record_command = ["rec", "-t", "wav", output_file]

    print("开始录音... 按 Enter 停止录音")
    print(f"保存路径: {output_file}")
    process = subprocess.Popen(record_command)

    try:
        input("按 Enter 停止录音...")
        process.send_signal(signal.SIGINT)
        process.wait()
        print(f"录音已停止，文件保存至: {output_file}")
    except KeyboardInterrupt:
        process.terminate()
        print(f"\n录音中断，文件保存至: {output_file}")


if __name__ == "__main__":
    main()