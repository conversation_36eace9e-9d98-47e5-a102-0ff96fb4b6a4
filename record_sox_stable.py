#!/usr/bin/env python3
import subprocess
import signal
import os
import sys
import time
from datetime import datetime

def check_sox():
    """检查 SoX 是否安装"""
    try:
        result = subprocess.run(['which', 'rec'], capture_output=True, text=True)
        if result.returncode != 0:
            print("错误: 未找到 'rec' 命令。请安装 SoX:")
            print("brew install sox")
            return False
        return True
    except Exception as e:
        print(f"检查 SoX 时出错: {e}")
        return False

def main():
    # 检查 SoX
    if not check_sox():
        sys.exit(1)
    
    # 创建保存目录
    save_dir = "/Users/<USER>/Downloads/record_audio"
    try:
        os.makedirs(save_dir, exist_ok=True)
    except Exception as e:
        print(f"创建目录失败: {e}")
        sys.exit(1)
    
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(save_dir, f"recording_{timestamp}.wav")
    
    # 定义录音命令
    record_command = ["rec", "-t", "wav", output_file]
    
    print(f"开始录音...")
    print(f"保存路径: {output_file}")
    print("按 Enter 键停止录音")
    
    try:
        # 启动录音进程
        process = subprocess.Popen(
            record_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 检查进程是否成功启动
        time.sleep(0.2)
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print("录音进程启动失败:")
            if stdout:
                print(f"stdout: {stdout.decode()}")
            if stderr:
                print(f"stderr: {stderr.decode()}")
            return
        
        print("录音中... (按 Enter 停止)")
        
        # 等待用户按 Enter
        input()
        
        # 停止录音
        if process.poll() is None:
            print("正在停止录音...")
            process.send_signal(signal.SIGINT)
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
                print(f"录音已停止，文件保存至: {output_file}")
            except subprocess.TimeoutExpired:
                print("进程未响应，强制终止...")
                process.kill()
                process.wait()
                print(f"录音已强制停止，文件保存至: {output_file}")
        
        # 检查文件是否存在
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"文件大小: {file_size} 字节")
        else:
            print("警告: 录音文件未找到")
            
    except KeyboardInterrupt:
        print("\n录音被中断")
        if 'process' in locals() and process.poll() is None:
            process.terminate()
            process.wait()
        print(f"文件保存至: {output_file}")
        
    except Exception as e:
        print(f"录音过程中发生错误: {e}")
        if 'process' in locals() and process.poll() is None:
            process.terminate()
    
    finally:
        # 确保进程被清理
        if 'process' in locals() and process.poll() is None:
            try:
                process.terminate()
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()

if __name__ == "__main__":
    main()
