import subprocess
import signal
import os
import sys
import time
import shutil

def check_dependencies():
    """检查必要的依赖"""
    # 检查 rec 命令是否存在
    if not shutil.which("rec"):
        print("错误: 未找到 'rec' 命令。请安装 SoX:")
        print("brew install sox")
        return False
    
    # 检查 keyboard 库
    try:
        import keyboard
        return True
    except ImportError:
        print("错误: 未找到 'keyboard' 库。请安装:")
        print("pip install keyboard")
        return False

def main():
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    import keyboard
    
    # 定义录音命令，添加更多参数确保兼容性
    record_command = ["rec", "-t", "wav", "output.wav"]

    try:
        # 启动录音进程，添加错误处理
        print("开始录音... 按快捷键 Ctrl+Shift+A 停止录音")
        print("或者按 Ctrl+C 中断录音")
        
        process = subprocess.Popen(
            record_command, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
        )
        
        # 检查进程是否成功启动
        time.sleep(0.1)
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"录音进程启动失败:")
            print(f"stdout: {stdout.decode()}")
            print(f"stderr: {stderr.decode()}")
            return
        
        # 监听快捷键
        print("等待快捷键...")
        keyboard.wait("ctrl+shift+a")
        print("检测到停止快捷键，停止录音...")
        
        # 停止录音进程
        if process.poll() is None:
            process.send_signal(signal.SIGINT)
            # 等待进程优雅退出
            try:
                process.wait(timeout=5)
                print("录音已停止.")
            except subprocess.TimeoutExpired:
                print("进程未响应，强制终止...")
                process.kill()
                
    except KeyboardInterrupt:
        print("\n录音中断.")
    except Exception as e:
        print(f"发生错误: {e}")
        print("这可能是权限问题。请确保:")
        print("1. Python 有辅助功能权限")
        print("2. 应用有麦克风权限")
    finally:
        # 确保进程终止
        if 'process' in locals() and process.poll() is None:
            process.terminate()

if __name__ == "__main__":
    main()